# Intern Group Project 2025

## Summary
Create automated deployment of a complete IT stack to host a  website with basic logging and monitoring capabilities. Extra functionalities such as maintenance dashboards tools will be reach goals.  The deployment will be built using common industry tools and should both horizontally and vertically scalable along with a degree of flexibility and self healing.

## Milestone Goals
This is a basic

* [ ] Automated Deployment and configuration of K0s
  * [ ] Create the underlying infrastructure with Terraform
  * [ ] Configure K0s with Ansible
  * [ ] Achieve Idempotency and Enforcement of deployment and configuration

* [ ] Pipeline CI/CD
  * [ ] Build a gitlab pipeline for infrastructure deployment and configuration
  * [ ] Pipeline job to deploy Argo CD via Helm
  * [ ] Setup Gitops with ARGO CD to manage all other Helm Deployments
  * [ ] Deploy Harbor Image Registry

* [ ] Setup basic Authentication and Authorization
  * [ ] Setup IAM in AWS for operators and service accounts
  * [ ] Stand up OIDC with Vault
  * [ ] Control K0s access with Kyverno

* [ ] Build docker image hosting a custom website of choice.
  * [ ] Build the Docker Image. (reach goals, FROM scratch)
  * [ ] Build a Helm Chart for Website.
  * [ ] Automate container image build.
  * [ ] Push and pull image from the Image Registry

* [ ] Expand solution to handle dynamic pages.
  * [ ] Retrieve serve content from an external store (Dynamic Content)
  * [ ] Serve multi-tenant content based on a trigger.
  * [ ] Encrypt multi-tenant content (Key Management and Generation)

* [ ] Stand up the website in Kubernetes
  * [ ] Setup ingress controller with NGINX
  * [ ] Standup the Website.
  * [ ] Secure website with a certificate and Cert-Manager

* [ ] Logging and Monitoring
  * [ ] Setup Grafana for Operator Dashboards
  * [ ] Setup Prometheus for Logging
  * [ ] Expose and Secure Grafana and Prometheus to the internet for Operation Users.


## References
* [K0s](https://k0sproject.io/)
* [Terraform]()
* [Ansible]()
* [Cert-Manager](https://cert-manager.io/)
