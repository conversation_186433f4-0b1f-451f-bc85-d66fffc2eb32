# Azure Development Accounts

**Updated:** 04/23/2025

This guide will instructs a Global (Tenant) Admin on creating user account in Azure Development.

[[_TOC_]]

## Prerequisites
* Global (Tenant) Admin Account
* Employee's ID (I-Number)
* Employee's email address.


## Create Account
* Login to either https://portal.azure.us or https://portal.azure.com with your Global (Tenant) Account
* Goto 'Users'
* Click 'New User' -> Create New User
  * Add User's Employee ID (I-Number) as the User Principal Name.
  * Add User's Full Name as Display Name
  * Copy the Password to be sent later.
* Click `Next`
  * Add First Name
  * Add User's SAP Email.
* Click `Assignments`
  * Add Group -> Add `_____-Contributors`
* Click `Review + Create`
* Click `Create`
