
Purpose: The purpose of this exercise is to familiarize new users with basic GCP concepts and resources.

Prerequisites: None

Goals: Show ability to implement core topics. Show understanding of additional concepts.

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Compute Engine
  * Compute Network
  * IAM
  * Logging & Monitoring
  * Firewall
  * Kubernetes Engine
  * Routing
  * Storage
    * Persistent Disk
    * Filestore
    * Cloud Storage
  * Tags
  * Labels
  * gcloud CLI

Learning Materials:
  * Google Certified Associate Cloud Engineer 2020
     https://learn.acloud.guru/course/gcp-certified-associate-cloud-engineer/dashboard
  * Deploying Resources to GCP with Terraform
    https://learn.acloud.guru/course/3078e33a-0a35-433b-a341-a45d3c46bd1d/overview
  * Google Certified Associate Cloud Engineer 2020 Practice Exam
    https://practice-exam.acloud.guru/gcp-certified-associate-cloud-engineer?courseId=gcp-certified-associate-cloud-engineer
  * Optional training material
    * Google Cloud Essentials
      https://learn.acloud.guru/course/b60292df-bf88-44f0-95e7-d8695494c4cb/overview
    * Introduction to Google Cloud
      https://learn.acloud.guru/course/d6fb013d-ab5b-440d-b2f0-ba51b650e6e2/overview
    * Crash Course on Google Cloud Platform
      https://learn.acloud.guru/course/gcp-101/overview
    * Google Kubernetes Engine (GKE): Beginner to Pro
      https://learn.acloud.guru/course/gke-beginner-to-pro/dashboard
  * Documentation
    * GCP
      https://cloud.google.com/docs
    * Terraform
      https://registry.terraform.io/providers/hashicorp/google/latest/docs
