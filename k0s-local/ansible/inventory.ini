[k0s_cluster]
k0s-local ansible_host=127.0.0.1 ansible_port=2222 ansible_user=k0s ansible_ssh_pass=k0s

[k0s_cluster:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
ansible_python_interpreter=/usr/bin/python3

[all:vars]
# K0s configuration variables
k0s_version=v1.33.1+k0s.1
k0s_config_dir=/etc/k0s
k0s_data_dir=/var/lib/k0s
k0s_user=k0s

# Cluster configuration
cluster_name=k0s-local
api_address=0.0.0.0
api_port=6443
pod_cidr=**********/16
service_cidr=*********/12
