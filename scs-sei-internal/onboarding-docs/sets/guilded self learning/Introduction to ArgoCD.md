Purpose: tbd

Prerequisites: tbd

Goals: Show ability to implement core topics. Show understanding of additional concepts

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Git
  * GitOps
  * Continuous Integration / Continuous Deployment fundamentals
  * Deploying ArgoCD
  * Connecting Git Repositories
  * Deploying an Application
    * Pointing ArgoCD app to either kustomization.yaml or values.yaml file
  * Triage and Logging
  * Fundamental Kubernetes Knowledge

Learning Materials:
  * Intro to ArgoCD: Kubernetes DevOps CI/CD by That DevOps Guy
https://www.youtube.com/watch?v=2WSJF7d8dUg
  * ArgoCD Tutorial for Beginners | GitOps CD for Kubernetes by TechWorld with Nana
https://www.youtube.com/watch?v=MeU5_k9ssrs
  * Awesome-Argo
https://github.com/terrytangyuan/awesome-argo
