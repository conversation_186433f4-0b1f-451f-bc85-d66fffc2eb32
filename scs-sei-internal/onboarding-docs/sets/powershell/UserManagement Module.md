# Summary
Use the process/steps described here to manage AWS Directory Service users with the User Management Powershell Module

## Table of Contents
[TOC]


## Target Audience
* engineers, All, (T1+)

## Terminology used
* `cmdlet` - A powershell function that can called directly or used within another script.
* `module` - A collection of powershell functions and data structures that can be loaded into memory (environment)
* `Active Directory` - shortened as `Directory` or `AD`. Microsoft's implementation of `LDAP` that contains objects representing Users, Groups, and Compute Instances.
* `OU Location` - short for `Organization Unit`.  `Active Directory` is divided and organized with a hieararchical tree of `OUs`


## Links
* [Microsoft: Powershell 101](https://learn.microsoft.com/en-us/powershell/scripting/learn/ps101/00-introduction)
* [Powershell Module](https://gitlab.core.sapns2.us/scs/shared/powershell/scripts/-/blob/main/active-directory-management/user-management/UserManagement.psm1)
* [SMS Constrained Endpoint Runbook](https://gitlab.core.sapns2.us/dev/ibp/documentation/-/blob/main/maintain/runbooks/deploy-constrained-endpoint.md)

## Differences between AWS and Traditional Active Directory Domains
* **Admin Account**: [Link](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ms_ad_manage_users_groups.html)

## Prerequisites
* TBD

## Quiz
* N/A


## User Management
Currently there are 3 known methods of managing users. `AWS Console`, `Windows RSAT`, and `UserManagement Module`.  A brief summary of `AWS Console` and `Windows RSAT` is provided before the detailed description of the `UserManagement Module`.

### AWS Console Management
There are some limited user management tools provided through AWS Console.

#### User Password Reset
* **Description:**
  * Anyone with full AWS Console access can reset any user password.
* **Steps:**
  1. <details><summary>From AWS Console Access choose "Directory Service"</summary><p>

      ![](./images/aws_directory_service.png)
      </p></details>
  1. Choose the Appropriate Directory Service ID
  1. <details><summary>Click on "Reset user password" </summary><p>

      ![](./images/reset_user_password_button.png)
      </p></details>
  1. <details><summary>Fill in the appropriate forms and "Reset"</summary><p>

      ![](./images/reset_user_password_form.png)

#### Workspace Users
* **Description:**
  * New users can be created while also creating a workspace.  However you will be unable to add these users to any `AD Groups` from this interface.

  * **NOTE:** This is informational only.  User creation should be done with the powershell module. See Constrained Endpoint
* **Steps:**
  1. <details><summary>From AWS Console Access choose "Workspace"</summary><p>

      ![](./images/workspace_service.png)
      </p></details>
  1. <details><summary>Choose the Workspaces and Click "Create Workspaces"</summary><p>

      ![](./images/create_workspace_button.png)
      </p></details>
  1. Choose the appropriate Directory Service ID and click "Next"
  1. Click "Create Additional User"
  1. <details><summary>On step 2 Click"Create Users" Fill in the appropriate forms and click "Next"</summary><p>

      ![](./images/create_users_workspace.png)


### Windows RSAT
Windows Remote Server Administration Tools (RSAT) is the traditional way of managing an `AD Domain`.  These remote tools can be installed on any machine, virtual machine, or AWS Workspace that is joined to the domain.

#### RSAT Installation
* **Description:**
  * Install Windows RSAT Tools on your machine.
* **Steps:**
  * AWS Provides [documentation on how to install RSAT](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ms_ad_install_ad_tools.html) on a Virtual Machine

#### Directory Service Administration
* **Description:**
  * How to manage users with RSAT
  * **NOTE:** User creation should not be done in RSAT. Instead, use the powershell module. See [UsermManagement Module Administration](#usermanagement-module-administration)
* **Steps:**
  1. <details><summary>Start "Active Directory Users and Computers"</summary><p>

      ![](./images/aduc1.png)
      <details><summary>Also known as "dsa.msc"</summary><p>

        ![](./images/dsa1.png)
        </p></details>
      <details><summary>Or from "Server Manager"</summary><p>

        ![](./images/server_manager.png)
        </p></details>
      </p></details>
  1. <details><summary>Click on the Search button and Search for your user</summary><p>

      ![](./images/search_aduc.png)
      </p></details>
  1. <details><summary>Right Click on user and select Reset Password</summary><p>

      ![](./images/aduc_reset_password.png)
      </p></details>
  1. Right Click on user and select Properties....
      * <details><summary>Select Account to unlock user</summary><p>

        ![](./images/aduc_unlock_account.png)
        </p></details>
      * <details><summary>Select "Member Of" to modify group membership</summary><p>

        ![](./images/aduc_set_groups.png)
        </p></details>


### UserManagement Module Administration
The Constrained Endpoint is a headless windows server with RSAT tools installed and configured to allow SSH access.  Additionally, the Sovereign Cloud UserManagement Powershell modules have been installed.

**NOTE:** While the powershell module can also be run from a windows desktop/workspace, it was designed to be run from the constrained endpoints.

**NOTE:** Not all available commands and functions will be covered in this documentation.  Only the most common or popular.  Please refer to the [powershell module](https://gitlab.core.sapns2.us/scs/shared/powershell/scripts/-/blob/main/active-directory-management/user-management/UserManagement.psm1?ref_type=heads) for complete documentation.


#### Overview
The UserManagement Module is a self contained module that uses a single source of truth, referred to as the `user.csv`, to provide cmdlets or functions useful in the day to day management of active driectory users. Like all powershell functions, these commands can be run by hand, referenced from another script, or be scheduled to run automatically.

* NOTE: This document will not cover architecture, endpoint connectivity, or how to transfer the `user.csv` to and from an endpoint.

#### Requirements
The UserManagement Module requires the following to run correctly.
* Powershell 5.1 Environment
* RSAT Tools installed
* Domain Administrator Priveleges or equivalent User Management Privileges.


#### User CSV File Explanation
* <details><summary>Expand</summary><p>

  * The User CSV file contains the following fields
    ```csv
    FirstName,LastName,Id,Email,Company,Groups,OUPath,Password,Authorization,Workspace
    ```
  * `Password`, `Authorization`, and `Workspace` are legacy values and can be left blank.
  * `OUPath` should be copied from existing examples
    * The `OUPath` Attribute determines the organizational unit where the object will reside in Active Directory
    * This field should only be changed by an experienced Active Directory Administrator.
  * `Groups` is an Array (List) of group names that the user will be a member of.
  * `FirstName`, `LastName`, `Email`, and `Company` are strings that will populate the related Active Directory Object Attribute
  * `Id` will populate the samAccountName.  **_The Script will use this attribute as the KEY Index._**
  * <details><summary>Example Line</summary><p>

    ```csv
    FirstName,LastName,Id,Email,Company,Groups,OUPath,Password,Authorization,Workspace
    test,test2,i12345,<EMAIL>,SCS,"rbac_role_group_3,rbac_role_group_2","OU=Users,OU=tier1,OU=Management,OU=SCS",,,
    ```
    </p></details>
  </p></details>

#### UserManagement Module Commands
##### How to Import the Module
* <details><summary>Expand</summary><p>

  Importing a powershell module is the first step to using a module, see below:

  ```powershell
  cd c:/temp
  Import-Module ./usermanagement.psm1
  ```
  </p></details>

##### List Available Module Commands
* <details><summary>Expand</summary><p>

  Use the following to list available commands in the Module
  ```powershell

  Get-Command -Module UserManagement
  ```
  </p></details>

##### Use the Built-In Help and Documentation
* <details><summary>Expand</summary><p>

  ```powershell

  Get-Help ___COMMAND-NAME___
  ```
  </p></details>

##### Create New Users and Enforce Idempotency
* <details><summary>Expand</summary><p>

  This command will check the `user.csv` against the actual `Directory`.  The function will do the following:
  * Create any missing users
  * Update missing or incorrect attributes
  * Move users to the correct `OU Location`

  ```powershell

  # dry run
  New-BulkUsers -UserListPath c:/temp/users.csv
  # apply
  New-BulkUsers -UserListPath c:/temp/users.csv -Apply
  ```
  </p></details>

##### Set User Groups
* <details><summary>Expand</summary><p>

  This command will check the groups listed for a user in the `user.csv` against the actual `Directory`.  The function will do the following:
  * Ensure the user is in the groups listed in the `user.csv`
  * Remove user from groups not listed in the `user.csv`

  ```powershell

  # dry run
  Set-UserGroups -UserListPath c:/temp/users.csv
  # apply
  Set-UserGroups -UserListPath c:/temp/users.csv -Apply
  ```
  </p></details>

##### Get User Status
* <details><summary>Overview</summary><p>

  This command will find a matching user and display relavent information.  The function will perform a partial match on the following attributes:
  * samAccountName (i-number)
  * First Name
  * Last Name

  The output of this command is a list when searching by specific users.  If searching on a `user.csv` instead, the output is a table.  The attributes in the output can be modified by different `views` that are applied.  Notable attributes in the default output include:
  * Enabled Status
  * Created and Modified Dates
  * Last Bad Password
  * Locked-Out Status
  * Group Membership

  Please refer to the built-in documentation for a full description of all available options.
  </p></details>

* <details><summary>Basic Usage</summary><p>

  ```powershell

  Get-NS2User
  # -or
  Get-NS2User ___I-NUMBER1___,___I-NUMBER2___
  ```
  </p></details>

* <details><summary>Applying Views</summary><p>
  
  Current Possible Views:
  * Default - ViewDetailed when choosing by username. ViewGetFresh when choosing by csv file
  * ViewDetailed - All Attributes, sorted by samAccountAttribute
  * ViewGetFresh - Sorted by Enabled Status, Smaller subset of attributes than Detailed.
  * ViewLastLogon - Sorted by Enabled Status Selection of Name and Logon Date Metrics
  * ViewLocked - Selection of ID, Name, Status, and Last Bad Password Attempt.

  ```powershell

  Get-NS2User ___I-NUMBER1___,___I-NUMBER2___ -ViewSelect ViewLocked
  # -or
  Get-NS2User users.csv -ViewSelect ViewLastLogon
  ```
  </p></details>

##### Reset User Passwords
* <details><summary>Expand</summary><p>

  (Multiple) User Passwords can be reset with autogenerated passwords output to file.
  ```powershell

  Reset-BulkPasswords -Users ___INSERT-I-NUMBER1___,___INSERT-I-NUMBER2___
  ```
  * NOTE: Passwords will be written to an output file.
  </p></details>

##### Disable Users not found in CSV
* <details><summary>Expand</summary><p>

  ```powershell

  # dry run
  Disable-BulkUsers -UserListPath c:\temp\users.csv
  # apply
  Disable-BulkUsers -UserListPath c:\temp\users.csv -Apply
  ```
  * NOTE: Will only be disabled, not deleted.  Please use manual console to actually delete.
  </p></details>
