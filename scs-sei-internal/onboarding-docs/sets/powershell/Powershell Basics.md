# Powershell Basics
___UNDER CONSTRUCTION___

## Summary:
Learn how to perform basic powershell commands.

## Goals:
* Understand Powershell Versions
* Understand Cmdlets and how to run them.
* Understand How to Get Help.
* Understand Functions and Modules.

## Target Audience
* engineers, All, (T1+)

## Terminology used
* `cmdlet` - 

## Links
* [Microsoft: Powershell 101](https://learn.microsoft.com/en-us/powershell/scripting/learn/ps101/00-introduction)

## Prerequisites
* [Terraform Basics](./Terraform%20Basics.md)

## Quiz
* N/A

## Instructions
### Checking Powershell Version
**Problem**
You want to start powershell and check the version.

**NOTE:**
The latest is Powershell 7.4 and is a cross-platform scripting language and binary.  Powershell 5.1 is no longer in development, however it is still in active use because many Microsoft native features have not been ported into the latest cross-platform version.  Chief among these missing features is native integration with Microsoft Active Directory.

**Solution**
* See the following link for [installing powershell](https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell)
* The binary `pwsh` can be used to start a powershell session.
* The version of powershell can be shown with the command `write-output $PSVersionTable` run in a powershell session.
  * NOTE: This can be shortend by ommitting the implied cmdlet to jsut `PSVersionTable`.
  * NOTE: Powershell is not case sensitive.

**Explanation** 
* <details><summary>expand</summary><p>

  ```pwsh
  PS > $psversiontable

  Name                           Value
  ----                           -----
  PSVersion                      7.3.5
  PSEdition                      Core
  GitCommitId                    7.3.5
  OS                             Darwin 23.0.0 Darwin Kernel Version 23.0.0: Fri Sep 15 14:41:34 PDT 2023; root:xnu-10002.1.13~1/RELEASE_ARM64_T8103
  Platform                       Unix
  PSCompatibleVersions           {1.0, 2.0, 3.0, 4.0…}
  PSRemotingProtocolVersion      2.3
  SerializationVersion           *******
  WSManStackVersion              3.0
  ```
  </p></details>


### Explanation of Cmdlets
**Problem**
Understand cmdlets, which are the very basic 

**Solution**
**Explanation**
