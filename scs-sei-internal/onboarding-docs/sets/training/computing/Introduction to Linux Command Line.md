Content Last refreshed - 2021-10-20

Purpose: Familiarize new users with basic linux knowledge and useability from the command line (shell)

Goals: Be able to navigate the file system and perform basic file operations

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * SSH
    * Connecting to remote hosts
    * host keys vs username/password
      * dealing with hostkey mismatch
    * options: -i  -k  -u -v -A
    * ssh agent
    * ssh keygen
    * config file
    * RSYNC
  * File and Directory Structure
    * Navigation
    * Permission and ownership
    * Creating files
    * Moving directories and content
    * Searching for strings
  * Modifying files
    * text editor e.g., vi
  * Permissions
    * Privilege escalation via sudo/dzdo
  * Viewing Logs
    * tail
  * Viewing usage
    * man pages
  * Application management
    * Installing and removing applications
    * Linux distribution variants

Learning Materials:
  * Linkedin Learning
    * [Learning Linux Command Line](https://www.linkedin.com/learning/learning-linux-command-line-14447912)
    * [Linux System Engineer, Networking and SSH](https://www.linkedin.com/learning/linux-system-engineer-networking-and-ssh/demystify-linux-networking-and-ssh)

  * Other online resources
  https://ryanstutorials.net/linuxtutorial/
    * Optional - Filters
    * Optional - Grep and Regular Expressions
    * Optional - Piping and Redirection
    * Optional - Process Management
    * Optional - Scripting
    * Optional - Cheat Sheet

Advanced Materials:
