# Basic Terraform Commands

## Summary:
This document covers basic terraform commands.

## Goals:
* Basic Commands
  * Version
  * Plan
  * Apply
  * Init


## Target Audience
* Engineers, All, (T1+)

## Terminology used
* `terraform version` - Shows current list of terraform and provider versions used.
* `terraform init` - Scans code and modules to ensure that required providers are installed and state files are present.
* `terraform plan` - Does a dry run of the current terraform code found in the folder. Output can be saved for future use.
* `terraform apply` - Performs both a dry run and confirmation before. Can accept previously saved plans.

## Prerequisites
* Terraform Binary Installed

## Quiz

## Instructions
