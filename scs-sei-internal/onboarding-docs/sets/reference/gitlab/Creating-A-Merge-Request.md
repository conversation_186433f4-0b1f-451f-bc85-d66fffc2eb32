


# Creating a Merge Request in Gitlab
[TOC]

## Summary:
This document covers the fundamentals of a Gitlab Merge Request and explains some of the terminology and processes.  

This document assumes the usage of VSCode, but any equivalent IDE may be used.

Please consult your Team's <PERSON>rum Master for the proper workflow procedures as this may differ from team to team.

## Goals:
* Create a Gitlab Branch
* Create a Gitlab Merge Request

## Target Audience
* All

## Terminology used
* `IDE` - Integrated Development Environment.  Refers to any application whose purpose is to provide an environment for software development.
* `VSCode` - A cross-platform free IDE. https://code.visualstudio.com
* `git` - A version control system. https://git-scm.com/
* `branch` - An (alternate) version of a set of files.  [Detailed Explanation](https://git-scm.com/book/en/v2/Git-Branching-Branches-in-a-Nutshell)

## Dependencies:
* VSCode
* Gitlab Access
* Workstation Configured with GIT  ___LINK_TO_PREVIOUS_GIT_ARTICLE_HERE___

## Quiz
* Link to Quiz HERE


## Topics
### Git Configuration
If this is the first time using `git`. You will need to register your user name and email address.

* <details><summary>Step by Step Instructions</summary><p>

  1. Open terminal.
  1. Use the following commands, substituting appropriate values:
    ```sh
    git config --global user.name "___FIRST_NAME___ ___LAST_NAME___"
    git config --global user.email "___MY_NAME_@_EXAMPLE.COM___"
    ```
  </p></details>

### Git Clone Repository
Clone the desired repository locally to your workstation.

* <details><summary>Step by Step Instructions</summary><p>
  
  1. Create a folder on your workstation where you will clone the repository locally.
  1. Create additional folder structure as desired.
  1. In an empty folder, use `git` to clone your desired repository.
  </p></details>
* <details><summary>Command Line Instructions</summary><p>

  ```sh
  mkdir /EXAMPLE_FOLDER
  mkdir /EXAMPLE_FOLDER/EXAMPLE_SUBFOLDER
  cd /EXAMPLE_FOLDER/EXAMPLE_SUBFOLDER
  git clone --recursive ___Repository URI___
  ```  
  </p></details>

### VSCode Source Control
In VSCode, if you open a file from a cloned repository. That repository will appear in the source control panel.  You will be able to create branches from within VSCode.

* <details><summary>Step by Step Instructions</summary><p>

  1. In VSCode, once you open a file from a repository, it will add that repository to the build in source control.
      * <details><summary>VSCode Source Control Image</summary><p>

        ![](./images/vscode_source_control.png)
        </p></details> 
  1. You should see the current branch you are working at the bottem left.
      * <details><summary>VSCode current branch</summary><p>

        ![](./images/vscode_branch.png)
        </p></details>
  1. Click on the branch name to create a new branch. 
      * To create a branch using the current files choose `Create a new branch`
      * To create a branch based on another branch choose `Create a new branch from`
      * <details><summary>VSCode Create Branch</summary><p>

        ![](./images/vscode_create_branch.png)
        </p></details>
  </p></details>

## Create Merge Request
In a web browser, navigate to the repository in the gitlab website.  Create a merge request using your newly created branch.  From this point, follow appropriate workflow steps as set by your SCRUM Leader.

* <details><summary>Step by Step Instructions</summary><p>

  1. Navigate to gitlab in your browser
  1. Navigate to the repository you are working in.
  1. <details><summary>Click the `+` symbol to create a merge request</summary>

     ![](./images/create_merge_request.png)
     </details>
  1. 
___TO BE COMPLETED___
