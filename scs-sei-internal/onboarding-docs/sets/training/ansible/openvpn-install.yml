---

# Playbook Name: openvpn-install.yml
# Description: Basic playbook for using the OpenVPN Role to install.
# Prerequisites:
#   - RHEL or Ubuntu
#   - OpenVPN Role
# Variables:
# Examples Usage:
# Authors: <AUTHORS>
# Version: 2.9-000001
# Modified: 2023-11-14 - Created
# Comments: None

  - hosts: all
    vars:
    pre_tasks:
    - name: Configure Base Repositories from S3
      include_role:
        name: repository-management
      vars:
        repo_enable: 'true'
        application_preset_selection: ['base','epel']

    tasks:
      - name: Install OpenVPN
        include_role:
          name: openvpn
          apply:
            become: true
        vars:
          clients: [vpnuser]
...
