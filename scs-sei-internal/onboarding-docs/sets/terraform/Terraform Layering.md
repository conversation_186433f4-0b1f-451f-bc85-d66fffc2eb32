# Terraform Layering

## Summary:
The following videos will introduce the concept of "layers" in terraform.

## Goals:
* Understand Terraform Layering

## Target Audience
* Engineers, All, (T1+)

## Terminology used
* `layers` - The concept of breaking up larger monolithic automation into smaller hieararchical pieces to limit scope and impact.

## Prerequisites
* [Terraform Basics](Terraform%20Basics.md)

## Quiz

## Links
Use the following link to access the documentation:

* [Documentation: Blog Terraform iac multi-layering](https://www.padok.fr/en/blog/terraform-iac-multi-layering)

* [Documentation: 3 layered approach to organizing terraform](https://www.lakshminp.com/3-layered-approach-to-organizing-terraform-code/
)
* [Video: Terraform mlti-layer architecture](https://www.youtube.com/watch?v=0UHkKZjM_iM)
