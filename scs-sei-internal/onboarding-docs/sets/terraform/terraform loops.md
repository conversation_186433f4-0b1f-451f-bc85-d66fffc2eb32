# Terraform Loops

## Summary:
Learn how to implement loops in terraform.

## Goals:
* Loop using `count`
* Loop using `for_each`
* Loop using `for`

## Target Audience
* engineers, All, (T1+)

## Terminology used
* `list` - A data structure. Also known as an array. A sequence of items presented as a group or collection.
* `ordered_list` - A data structure. Also known as an sorted array. A sorted sequence of items presented as a group or collection.
* `map` - A data structure. Also known has a dictionary or hash table. A collection of items organized as key/value pairs.
* `count` - Available for `resource` and `data` blocks. It will loop based on an `ordered list`.
* `for_each`- Available for `resource` and `data` blocks. It will loop based on a `map`
* `for` - Availble for `local` and `output` blocks.  It will loop as a `list` or `map` dependent on it's declaration.

## Links
* ["for" official documentation](https://developer.hashicorp.com/terraform/language/expressions/for)
* ["for_each" official documentation](https://developer.hashicorp.com/terraform/language/meta-arguments/for_each)
* ["count" official documentation](https://developer.hashicorp.com/terraform/language/meta-arguments/count)
* [Blog post on loops and ifs](https://blog.gruntwork.io/terraform-tips-tricks-loops-if-statements-and-gotchas-f739bbae55f9)

## Prerequisites
* [Terraform Basics](./Terraform%20Basics.md)

## Quiz
* N/A

## Instructions
**Problem:**
You want to repeat a certain action multiple times.

**NOTE:**
Looping in terraform _does not exist_ as a language construct.
Rather looping and all it's variants (count, for, for_each) is a resource-type construct.

This means it's implementation and use is specific to the resource-type that it's defined in.  There is not a universal behavior across the entire language.

### `Count` Looping
* <details><summary>expand</summary><p>

  **Solution:**
  As of Terraform 0.13+, the `count` expression may be used in in module declarations and most resource-types:

  **NOTE:**
  _Caution!_ When using the `count` to iterate, resources are stored in the state as an **_ordered_** list. This means modifications and changes to the loop can be **_destructive_**.

  As of Terraform 1.1+, terraform supports ["implicit moves"](https://developer.hashicorp.com/terraform/language/modules/develop/refactoring) in regards to the count attribute.

  **Explanation:**
  * <details><summary>The following demonstrates using "count" in resource, data, and module declarations.</summary><p>    

    The terraform below shows how count is used per resource type (`module`, `data`, and `resource`) to iterate a specific number of time independently of each other. 
    
    Reminder: the result of this loop is an ordered list of each resource being created.  Any changes will be destructive from the element being changed and any subsequent element.  If I have a list of `[1,2,3,4,5]` and I make a change to remove element `3`.  The end result will be elements `[3,4,5]` being destroyed and elements `[4,5]` being recreated as `[3,4]`

    ```terraform
    locals { count = 3 }
    module "module_name" {
      count  = local.count
      source = "./module"
    }
    data "null_data_source" "data_name" {
      count = local.count
      # ....etc
    }
    resource "null_resource" "resource_name" {
      count = local.count
      # ....etc
    }
    output "module_output" { value = module.module_name }
    output "data_output" { value = data.null_data_source.data_name }
    output "resource_output" { value = resource.null_resource.resource_name }
    ```
    </p></details>
  </p></details>

### `for` Looping
* <details><summary>expand</summary>

  **Solution:**
  You can use the "for" expression to iterate on value-types. i.e. locals, and outputs.

  **Explanation:**
  In terraform, for loops can produce a list or a map. They also have an `if` subexpression that can be used to filter selection.

  * <details><summary>The following demonstrates a `for` loop with a list output.</summary>

    ```terraform
    locals { mylist = ["1", "2", "3"] }
    output "locals_output" { value = [for key, value in local.mylist : value] }
    ```
    </details>

  * <details><summary>The following demonstrates a `for` loop with an if filter.</summary>

    ```terraform
    locals {
      mylist          = ["1", "2", "3"]
      myfiltered_list = [for key, value in local.mylist : "Match on list[${key}]" if value == "2"]
    }
    output "locals_output" { value = local.myfiltered_list }
    ```
    </details>

  * <details><summary>The following demonstrates a `for` loop with a mapped output.</summary>

    ```terraform
    locals {
      mylist          = ["1", "2", "3"]
      mylist2         = ["a", "b", "c"]
      myfiltered_list = { for key, value in local.mylist : value => local.mylist2[key] }
    }
    output "locals_output" { value = local.myfiltered_list }
    ```
    </details>

  * <details><summary>The following demonstrates a nested `for` loop with a mapped output.</summary>

    ```terraform
    locals {
      mylist  = ["1", "2", "3"]
      mylist2 = ["a", "b", "c"]
      myfiltered_list = {
        for key, value in local.mylist : tostring(value + 10) => {
          for subkey, subvalue in local.mylist2 : subkey => subvalue
          if(value <= "2") && (subvalue == "a")
        }
        if key != 0
      }
    }
    output "locals_output" { value = local.myfiltered_list }
    ```
    </details>
  </details>

### `for_each` Looping
* <details><summary>expand</summary>

  **Solution:**
  As an alternative to count you can use the "for_each" expression to iterate on resource-types. i.e. data, resource, and module.

  **Explanation:**
  * <details><summary>Using for_each to iterate in different resource-types</summary>

    ```terraform
    locals {
      mylist = ["1", "2", "3"]
      mymap = {
        1 = "a"
        2 = "b"
        3 = "c"
      }
    }
    module "module_name" {
      for_each = toset(local.mylist)
      source   = "./module"
    }
    data "null_data_source" "data_name" {
      for_each = local.mymap
      # ....etc
    }
    resource "null_resource" "resource_name" {
      for_each = local.mymap
      # ....etc
    }
    output "module_output" { value = module.module_name }
    output "data_output" { value = data.null_data_source.data_name }
    output "resource_output" { value = resource.null_resource.resource_name }
    ```
    </details>
  </details>
