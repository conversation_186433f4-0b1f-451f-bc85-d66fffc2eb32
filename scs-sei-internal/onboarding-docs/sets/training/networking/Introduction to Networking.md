Content Last refreshed - 2025-06-09

Purpose: Familiarize people with core concepts of networking and how they relate to AWS networking.

Goals: Fundamental understanding of core network concepts listed below.

Completion Criteria: Demonstrate to understanding of basic networking to mentor.

Dependencies:
  * AWS

Topics:
  * Basic networking
    * OSI Model - Brief Understanding
      * https://www.linkedin.com/learning/networking-foundations-networking-basics/learning-the-seven-layers-24999853
      * Understand what the concept of the OSI model is.
        * Understand general overview of each layer.
        * Detailed knowledge of each layer is NOT required for basic understanding.
      * Understand the concept of encapsulation
        * https://en.wikipedia.org/wiki/Encapsulation_(networking)
    * Network Protocols (Layer 3/4)
      * https://www.linkedin.com/learning/comptia-a-plus-core-1-220-1101-cert-prep/tcp-udp-and-icmp-21427236?autoplay=true&resume=false&u=57692769
      * Basic ICMP
        □ Understand that ICMP is a Layer 3 protocol commonly used to troubleshoot network connectivity. (Ping traceroute)
      * Basic TCP
        □ Understand that the protocol is connection-oriented and error checked.
      * Basic UDP
        □ Understand that the protocol is connectionless and not error checked.
    * IP Addressing
      * Components of an IPv4 address
        * https://www.linkedin.com/learning/learning-ip-addressing-2/ip-addressing-for-internet-communications
        * https://www.linkedin.com/learning/learning-subnetting-2019/
      * Subnetting
        * Understanding Binary
        * Network Masks
      * Classless CIDR blocks
        * Understand how to read a CIDR block.
        * Understand how to use a CIDR Table
          * Google "cidr table" or "cidr chart"
          * cidr calculator: https://www.ipaddressguide.com/cidr
          * cidr table: http://www.rjsmith.com/CIDR-Table.html
    * Ports
      * https://www.linkedin.com/learning/learning-the-packet-delivery-process-********/learn-port-basics
      * https://www.youtube.com/watch?v=qsZ8Qcm6_8k
      * https://www.youtube.com/watch?v=AXrFCbD4-fU
      * Understand each IP Address can connect on multiple ports
      * Know how to use netstat to  list and read the port status on a host.
    * Crossing the boundary - NAT and PAT
      * Basic Routing
        * Understand how to read a basic routing table
        * https://www.youtube.com/watch?v=g8eP4fhrx3I  Change the playback speed option if the guy talks too slow for you :)
        * https://www.linkedin.com/learning/cisco-networking-foundations-switching-and-routing/static-and-default-routes
      * NAT and PAT
        * Understand basic concept of address translation
        * Understand the role of the gateway/router in assigning NAT
        * https://www.youtube.com/watch?v=wg8Hosr20yw
        * https://www.youtube.com/watch?v=qij5qpHcbBk

  * AWS Networking
    * https://www.linkedin.com/learning/aws-networking-********/aws-networking-overview
    * Nat Gateways
      * Nat Gateways effectively provide one way outbound network traffic.
      * https://docs.aws.amazon.com/vpc/latest/userguide/vpc-nat-gateway.html

  * General Networking
    * DNS Basics
      * https://www.linkedin.com/learning/comptia-a-plus-core-1-220-1101-cert-prep/understanding-dns-********
      * Understand authoritative servers
      * Understand recursive/caching servers
    * DNS Domains and Subdomain hierarchy
      * Understand Top Level Domains (.TLD)
      * Understand Fully Qualified Domains (FQDN)
      * Understand how subdomains work.
    * DNS Query
      * Understand the process of a DNS Query
      * Understand how the host file.
      * Know basic use basic tools:
        □ nslookup
        □ dig
      * https://en.wikipedia.org/wiki/Hosts_(file)

Advanced Topics:
  * Firewalls
    * Stateful connections
  * Proxies, Reverse Proxies
  * Load Balancers
  * IPv6
  * Application protocols (Layer 7)
    * FTP
    * HTTP
    * HTTPS
    * DNS (See Fundamentals - DNS)
    * DHCP
    * etc.
  * AWS VPC Peering
  * Webtraffic
    * basic HTTP Requests
  * VPN
    * IPSec
    * SSL
  * Switching (Layer 2)
    * MAC address table
    * VLANs and trunking
  * Routing methods (Layer 3)
    * Interior gateway/exterior gateway routing protocols
    * Distance vector/link state/path vector protocols
    * RIP
    * RIPv2
    * OSPF
    * IGRP
    * EIGRP
    * BGP

Learning Materials:
  * See inline links above.
