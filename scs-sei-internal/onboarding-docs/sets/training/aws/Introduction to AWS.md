Purpose: Familiarize new users with basic aws concepts via aws console.

Goals: Understand and create resources listed in the topics

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * VPC (Virtual Private Cloud)
    * subnet (public vs private)
    * IGW vs NGW
    * basic networking e.g., cidr blocks, ip address ranges
    * EIP, Public IP, Private IP
    * Security Groups
      * Security Groups vs ACL
  * Route53
  * EC2 (Elastic Compute Cloud)
    * tags
    * instance types
    * load balancer
    * images
    * SSH keys as pertains to AWS and Linux logins
      * key/pair
      * ssh keys
  * S3 (Simple Storage Service)
    * create
  * AWS CLI (advanced)
    * https://docs.aws.amazon.com/cli/index.html
    * regions
    * endpoints

Learning Materials:
  * A Cloud Guru - AWS Essentials: https://learn.acloud.guru/course/2e313aa3-60db-4fc9-9fde-c520d69a14d4/overview
    * Networking Services and Connectivity
      * Mandatory - Virtual Private Cloud (VPC)
      * Optional - Virtual Private Cloud (VPC) [Chapter 9]
      * Mandatory - Elastic Compute Cloud (EC2)
      * Optional - Elastic Compute Cloud (EC2) [Chapter 5]
      * Optional - Internet Gateways (IGW)
      * Optional - Route Tables (RTs)
      * Optional - Network Access Control Lists (NACLs)
      * Optional - Availability Zones (AZs)
    * Compute Services
      * Optional - Elastic Block Storage (EBS)
    * Storage Services
      * Optional - Object Lifecycles
      * Optional - Storage Classes
    * Load Balancing, Elasticity, and Scalability
      * Elastic Load Balancer (ELB)
  * Amazon AWS Documentation
    * AWS CLI : https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html
    * AWS VPC: https://docs.aws.amazon.com/vpc/latest/userguide/what-is-amazon-vpc.html
    * AWS EC2: https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/concepts.html

  * Unvalidated Resources
    * Tagging
      * https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Using_Tags.html
      * https://d1.awsstatic.com/whitepapers/aws-tagging-best-practices.pdf
      * https://aws.amazon.com/answers/account-management/aws-tagging-strategies/
    * Keypair best practices - put a dang password
    * SSH
      * https://www.ssh.com/ssh/keygen/
      * https://www.howtoforge.com/linux-basics-how-to-install-ssh-keys-on-the-shell
      * https://www.youtube.com/watch?v=DbPDraCYju8
