# Using Docker Workspace

**Updated:** 04/21/2025

This guide will cover using STE Workspaces

[[_TOC_]]

## Prerequisites
* Docker Installed
* Docker Pull of Images
* Copy of STE Docker Compose File (Optional)

## Install Docker and Pull Images
* If you have not installed Dock<PERSON> or Pulled the images, please follow the instructions here: [LINK](./Workstation-Setup.md)

## Clone STE Global Repository
* If you have not cloned the STE Global Repository, please follow the instructions here: [LINK](../gitlab/Workstation-Setup.md#workspace-setup)

## Configure Docker Compose ENV
Configure the Docker compose `.env`. This guide assumes the repository was cloned to `~/Projects/SovCloud/scs/ste/automation`.
This guide assumes you will run the compose file from `~/Projects/docker-compose`

1. Start Docker Desktop if it hasn't been started.
1. Make the docker-compose folder.
1. Copy the env-template file to docker-compose folder as `.env`
1. Copy the compose file to the docker-compose folder.
1. Update the `.env` files
    * ARTIFACT_SERVER  should be either harbor.dev.ste.dev.scs.sap or scs-ste-etd-containers-public-prod.common.repositories.cloud.sap
    * MY_REPOS_PATH  will mount your local folder into the container as /mnt/repos 
    * STE_AUTOMATION  should point to the local clone of the STE Automation Repo
1. Start Docker Compose in Detached mode
1. Attach to desired workspace.


```sh
mkdir -p ~/Projects/docker-compose
cp ~/Projects/SovCloud/scs/ste/automation/containers/dev_general/.env-template ~/Projects/docker-compose/.env
cp ~/Projects/SovCloud/scs/ste/automation/containers/dev_general/compose.yml ~/Projects/docker-compose/
cd ~/Projects/docker-compose/
vi .env
```

## Docker Compose Quick start
1. Starting ste_controller only: `docker compose up -d ste_controller`
1. Attach to ste_controller:  `docker compose attach ste_controller`
1. View MOTD: `cat /etc/motd`
1. Validate correct volume mounting: `ansible-playbook $STE_AUTOMATION/ansible/playbooks/tools/test-ste-roles-path.yml`

## Basic Docker Commands
`docker pull <image>:<version>`: Pulls an image from a registry
`docker run -it --rm <image>:<version`: Runs a container from an image in interactive mode
`docker ps`: Lists all running containers
`docker ps -a`: Lists all containers
`docker image ls`: Lists all images
`ctl+p+q`: Detaches from a running containers

## Basic Docker Compose
`docker compose up -d` : Starts the services defined in the docker-compose.yml file in detached mode
`docker compose down` : Stops the services defined in the docker-compose.yml file
`docker compose ps` : Lists running services in compose file.
`docker compose attach <container_id>` : Attaches to a running service
