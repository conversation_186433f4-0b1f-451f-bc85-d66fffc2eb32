Content Last refreshed - 2021-10-20

Purpose: Familiarize new users with basic powershell knowledge and useability from the command line.

Goals:  Demonstrate understanding of core Powershell topics

Completion Criteria: Create an use a powershell module to manipulate accounts in Active Directory

Topics:
  * Objects
  * Variables
  * Cmdlet
  * Comparison Operators
  * Arrays = Lists
  * Hashtables = Dictionaries
  * Modules
  * Loops
  * Functions
  * Parameters

Recommended Editors
  * Powershell ISE
    * Native to windows operating system (requires Windows GUI however)
    * Includes intellisense (auto completion, definitions etc.)
    * Does not include any linting
    * Also is technically deprecated by Microsoft
  * VSCode
    * Cross platform
    * Includes Intellisense
    * Includes PSScript Analyzer (Best Practice linting)

Best Practice
  * Always include Comment Headers (Comment-based help)
  * Never abbreviate
  * Do NOT leave trailing spaces (VSC<PERSON> can automatically trim)
  * Adhere to Approved Verbs as much as possible

Resources:
  * A Cloud Guru beginner's course - ~1.5 hours
  * A Cloud Guru intermediate - PowerShell for Linux Admins - ~8.25 hours
  * Official Stuff
    * Getting started with Powershell
    * Official documentation (man pages)
    * Active Directory Module
    * https://www.powershellgallery.com/
  Official Unofficial Stuff
    * Best Practice Style Guide
    * Hey, Scripting Guy!
      * https://learn-powershell.net/
    * https://powershell.org/
      * Powershell Summit Videos
    * Links to various learning projects and videos
    * Learning Powershell

  Other third party
    * https://blog.udemy.com/powershell-tutorial/
    * https://www.lynda.com/search?q=powershell
    * Powershell to Python (or vice versa)

Quick Tips for getting Help
  * Get-Help <Cmdlet Name>  =  This is the equivalent to man.  Use parameters below to get more information
    * Get-Help -example                =   Provides examples of the Cmdlet in action
    * Get-Help -full                         =   Provides the full help section
  * Get-Command                           =  Shows specific or all available commands
  * Import-Module/Get-Module/Remove-Module    =   Manipulate current modules ( libraries)
  * Get-Member                              =  Describes an object

More Tips
  * Linux, Everything is a file.   Powershell, everything is an object.
    * What is an object?
    * Why is this important?
  * The best way to learn is by doing
    * Some simple tasks:
      * Find all users in Active Directory that belong to a specific group and sort by last name
      * Calculate your 401k
        □ leeholmes - Determining your 401k Contributions
      * Manipulate AWS with powershell
        □ https://aws.amazon.com/powershell/
      * Enumerate a directory structure and manipulate file permissions.
