# Workstation Setup

**Updated:** 04/21/2025

This guide will go over setting up your workstation for contributing to STE Build.

[[_TOC_]]

## Prerequisites
1. An SAP I/C/D Number (and SAP account)
1. An SAP Laptop
1. A FIPS-compliant Yubikey (You can order through Ariba)
1. You Gitlab account

## GitLab SSH Key setup
1. Generate your own SSH key (open a terminal and run the following)
```
% ssh-keygen -t ed25519
```
1. Copy the contents of your _public key_ (run the following command and copy the output):
```
% cat ~/.ssh/id_ed25519.pub
```
**NOTE:** Public key files always end in `.pub` (warning the other file is your private key, keep this a secret)

1. Login into the [Gitlab Web UI](https://gitlab.core.sapns2.us/)

2. Go to "Edit Profile":

    <details><summary>Edit Profile Image</summary><p>

    <img src=./images/gitlab-edit-profile.png width=25%/>
    </p></details>

3. Click "SSH Keys" on the left-side navigation bar. Click "Add a new key".
    <details><summary>SSH Keys</summary><p>

    <img src=./images/gitlab-ssh-keys.png width=25%/>
    </p></details>

4. Add New Key > Paste the content of your SSH _public key_ in the "Key" text box, then click the "Add key" button
    <details><summary>Add Key</summary><p>

    <img src=./images/gitlab-ssh-keys-add-key.png width=25%/>
    </p></details>

## Workspace Setup
Identify where you want your workspace to be locally, this guide will use `$HOME/Projects/SovCloud` as the example.

1. Create your workspace path (if it doesn't already exist)
```
mkdir -p ~/Projects/SovCloud
```

2. Change to that directory (all subsequent commands will expect you to be in this directory)
```
cd ~/Projects/SovCloud
```

3. Create skeleton directory structure to mimic Gitlab group structure:
```
# scs-sei-internal groups
mkdir -p scs-sei-internal
```

4. Start cloning the necessary Git repositories (if you get any git clone errors, make sure you completed your [GitLab SSH Key setup](#********************)):
```
# scs-sei-internal Global repos
pushd scs-sei-internal/onboarding-docs
<NAME_EMAIL>:scs-sei-internal/onboarding-docs.git
popd
# STUB for Kubernetes Development
```
