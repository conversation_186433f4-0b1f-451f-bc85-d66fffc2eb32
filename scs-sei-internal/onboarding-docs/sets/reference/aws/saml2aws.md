# SAML2AWS CLI Install

## Table of Contents
[TOC]

## Prerequisites
* Mac Laptop
* Mac Local Administrator
* [Homebrew](./homebrew.md)

## Information
* Adapted from [Homepage](https://gitlab.core.sapns2.us/coreservices/documentation/-/blob/main/aws-authentication-overview.md?ref_type=heads#aws-command-line)

## Quick Instructions
1. Brew Install `saml2aws` and `awscli`
1. Add entry in `.saml2aws` configuration
1. Login/Authenticate with `saml2aws login -a ___PROFILE_NAME___`
1. Export the AWS Profile with `export AWS_PROFILE=___PROFILE_NAME___`

## Detailed Instructions
<details><summary>Installation and Configuration</summary><p>

  1. Installation
      ```sh
      # For MacOS
      brew tap versent/homebrew-taps
      brew install saml2aws awscli

      # For Linux
      CURRENT_VERSION=2.28.4
      wget https://github.com/Versent/saml2aws/releases/download/v${CURRENT_VERSION}/saml2aws_${CURRENT_VERSION}_linux_amd64.tar.gz
      tar -xzvf saml2aws_${CURRENT_VERSION}_linux_amd64.tar.gz -C ~/.local/bin
      chmod u+x ~/.local/bin/saml2aws
      ```

  1. Configuration of SAML2AWS - Run the following in shell
      ```sh
      saml2aws configure --idp-provider KeyCloak --idp-account="core-gov" --region us-gov-west-1 --url https://sso.core.sapns2.us/auth/realms/core/protocol/saml/clients/aws-govcloud --profile=core-gov --session-duration=10800
      saml2aws configure --idp-provider KeyCloak --idp-account="core-com" --region us-east-2 --url https://sso.core.sapns2.us/auth/realms/core/protocol/saml/clients/aws --profile=core-cmm --session-duration=10800
      ```

  1. Select `KeyCloak` when prompted
  1. Accept AWS Profile (Name)
  1. Accept URL
  1. Use your `I-Number` for Username
  1. Add your `Core Password` for Password
  1. Confirm Password

  </p></details>

<details><summary>Usage</summary><p>

  1. Authenticate and Login
      ```sh
      saml2aws login -a ___PROFILE_NAME___ --force
      # --force is only necessary if you want to re-auth before the timeout
      ```
  1. Re-enter Username and Password (only necessary if recently changed)
  1. Input MFA
  1. Select Role (ibp-dev-g or spr-dev-g)
  1. Set AWS Profile
      ```sh
      export AWS_PROFILE=___PROFILE_NAME___
      ```
  </p></details>


## Appendix
* <details><summary>(Optional) Create SAML2AWS Profiles with pre-selected Roles</summary><p>

  * Open the config file `code ~/.saml2aws`
  * Create additional profiles as desired.
  * The value `aws_profile` should match an actual provile in `~/.aws/config`
  * Add the value `role_arn`.  This value can be obtained after you login. i.e. "selected role"
  * <details><summary>Example saml2aws profile</summary>

    ```
    [core-gov]
    app_id               =
    url                  = https://sso.core.sapns2.us/auth/realms/core/protocol/saml/clients/aws-govcloud
    username             = i99999
    provider             = KeyCloak
    mfa                  = Auto
    skip_verify          = false
    timeout              = 0
    aws_urn              = urn:amazon:webservices
    aws_session_duration = 10800
    aws_profile          = core-gov
    resource_id          =
    subdomain            =
    region               = us-gov-west-1
    http_attempts_count  =
    http_retry_delay     =
    <OMIT IF NOT USED> role_arn             = THIS IS AN OPTIONAL LINE TO PRESELECT THE ROLE
    ```
    </details>
  </p></details>

* <details><summary>(Optional) Install Devon's AWSProf Script</summary><p>

  This script makes it easier to switch between different `saml2aws` profiles
  * Add the following to your `~/.zshrc`
  * [awsprof](./awsprof.sh)
  * restart terminal
  </p></details>

### Validate AWS CLI Access
Validate your AWS CLI Access with the following commands:
```
saml2aws -a ___PROFILE_NAME___ login
awsprof ___PROFILE_NAME___
aws sts get-caller-identity
aws s3 ls
```

### Common Commands
```sh
# If default is set, login with default profile
saml2aws login

# Login with specified profile
saml2aws -a ___PROFILE_NAME___ login

# Force re-authentication before token expiration
saml2aws login --force

# Set the AWS Profile in Environment
export AWS_PROFILE=___PROFILE_NAME___

# Clear the AWS Profile in Environment
unset AWS_PROFILE
```
