Goals: tbd

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Interacting with your Cluster
    * kubeconfig
    * kubectl
    * kubectl exec
  * Cluster
    * System Nodes
    * Worker Nodes
  * Node
    * kubelet
  * Containers
    * Container runtimes
  * Workloads
    * Pods
      * Single container pods
      * Multi container pods
    * ReplicaSets
    * Deployments
    * StatefulSets
    * DaemonSets
    * Jobs/CronJobs
  * Networking
    * Ingress
    * Ingress Controllers
    * LoadBalancer
    * Service
  * Configuration
    * Secrets
    * ConfigMaps
  * Storage
    * PersistentVolumes
    * PersistentVolumeClaims
    * StorageClass
    * Secrets
    * ConfigMaps
    * Volume Mounts
  * ServiceAccounts

Advanced:
  * CustomResourceDefinitions

Learning Materials
  * A Cloud Guru: Kubernetes Essentials
https://learn.acloud.guru/course/2e0bad96-a602-4c91-9da2-e757d32abb8f/overview
  * Kubernetes by Example
http://kubebyexample.com/
  * A Cloud Guru: Certified Kubernetes Administrator
https://learn.acloud.guru/course/certified-kubernetes-administrator/overview
  * A Cloud Guru: Certified Kubernetes Application Developer (CKAD)
https://learn.acloud.guru/course/certified-kubernetes-application-developer/overview
  * EDX Kubernetes Introduction (potential for foundations)
https://www.edx.org/course/introduction-to-kubernetes
