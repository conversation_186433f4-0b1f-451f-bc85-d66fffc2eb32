# K0s Single-Node Kubernetes Cluster
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV K0S_VERSION=v1.33.1+k0s.1

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    openssh-server \
    python3 \
    python3-pip \
    sudo \
    iptables \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install k0s binary
RUN curl -sSLf https://get.k0s.sh | sudo sh

# Create k0s user
RUN useradd -m -s /bin/bash k0s && \
    echo "k0s:k0s" | chpasswd && \
    usermod -aG sudo k0s && \
    echo "k0s ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Configure SSH
RUN mkdir -p /var/run/sshd /run/sshd /var/log/supervisor && \
    echo 'root:root' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -A

# Create k0s config directory and configuration
RUN mkdir -p /etc/k0s

# Create supervisor configuration
COPY config/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose ports
EXPOSE 22 6443 10250

# Set working directory
WORKDIR /home/<USER>

# Use supervisor to manage services
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
