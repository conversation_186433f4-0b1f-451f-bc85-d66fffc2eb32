# Learning Path for Terraform Basics

## Table of Contents
[TOC]

## Installation and Configuration
* [Reference: Install with TFEnv](../sets/terraform/installing%20terraform.md)
* [Reference: Configure Terraform Plugin Cache](../sets/terraform/configure%20terraform%20plugin%20cache.md)

## Training Content
### Basic Terraform Commands
* [Reference: Basic Terraform Commands](../sets/terraform/basic%20terraform%20commands.md)

### Basic Terraform Concepts
* [Learning Item: Terraform Basics](../sets/terraform/Terraform%20Basics.md)

### Terraform Layering
* [Learning Item: Terraform Layering](../sets/terraform/Terraform%20Layering.md)

### Terraform Loops and Conditionals
* [Reference: Terraform Loops](../sets/terraform/terraform%20loops.md)
* [Reference: Terraform Ternary and Conditionals](../sets/terraform/terraform%20ternary.md)

## Reference Material
### Internal Terraform Example
The following is an internally written module that uses terraform to deploy a configurable AWS VPC with subnets and security groups. Usage can be seen in the `example-root` folder.

* [SovCloud Generic AWS Module](https://gitlab.core.sapns2.us/scs/shared/terraform/modules/-/tree/main/terraform-aws-network?ref_type=heads)

## Self Assessment and Validation
Validate that you understand the following Terraform Concepts

## Checklist of Concepts
* Terraform layers.
* Terraform Lists
* Terraform Maps (Dictionary)
* Terraform Loops
* Terraform Ternary

### Terraform Training Project
* [Complete this training project to demonstrate your proficiency](../sets/terraform/terraform_training_project_1.md)
