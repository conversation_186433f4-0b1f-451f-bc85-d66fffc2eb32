Purpose: tbd

Prerequisites: tbd

Goals: Show ability to implement core topics. Show understanding of additional concepts

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Kustomize
    * Command-line
    * kubectl -k
    * kustomize build
  * Kustomization.yaml
    * Using kustomize API to build resources
    * Secrets and ConfigMaps
    * Updating Container Images
    * Labels
    * Aggregating resources
    * Kustomize components
  * Applying changes to cluster
  * Folder Structure
    * Base
    * Overlays

Learning Materials:
  * YouTube - Simplifying Kubernetes YAML with Kustomize by That DevOps Guy
https://www.youtube.com/watch?v=5gsHYdiD6v8
  * YouTube - Kustomize - How to Simplify Kuberentes Configuration Management
https://www.youtube.com/watch?v=Twtbg6LFnAg
  * Kustomize Documentation
https://kubectl.docs.kubernetes.io/guides/
