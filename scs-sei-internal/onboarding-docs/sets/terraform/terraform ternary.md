# Terraform Ternary and Conditionals

## Summary:
Learn how to write conditional statements in terraform.

## Goals:
* Write a ternary using count.
* Write a ternary using for_each.

## Target Audience
* engineers, All, (T1+)

## Terminology used


## Prerequisites
* [Terraform Basics](./Terraform%20Basics.md)

## Quiz
* N/A

## Instructions
**Problem:**
You want to use filtering or conditional logic.

**Solution:**
Outside of `for` loops, terraform lacks an `if` statement as a language construct. Instead use a combination of looping and `ternary` statements to achieve similar behavior.

A ternary is simply a snippet of logic that assigns a value based on a conditional.  See the following:
```terraform
locals {
  conditional = true
  myvariable1 = local.conditional ? "Value1" : "Value2"
  myvariable = ("Alice" == "Bob") ? "True" : "False"
}
```

**Explanation:**
<details><summary>Assume ./module/module-main.tf</summary>

```terraform
variable "input_var" { type = string}
output "module_output" { value = var.input_var}
```
</details>

<details><summary>Using for_each to iterate in different resource-types</summary>

```terraform
locals {
  myvalue = false
  mymap = {
    one = "hello"
    two = "world"
  }
}
module "module_name" {
  for_each = local.myvalue != "true" ? local.mymap : {}
  source   = "./module"
  input_var = each.value
}
resource "null_resource" "resource_name" {
  count = local.myvalue == "true" ? 1 : 0
  # ....etc
}
output "module_output" { value = module.module_name }
output "resource_output" { value = resource.null_resource.resource_name }
```
</details>
