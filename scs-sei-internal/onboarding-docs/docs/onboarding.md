# Onboarding and Learning Path - Sustained Engineering Interns
This `Learning Path` is targeted towards onboarding a interns into the Sovereign Cloud Sustained Engineering Organization.

Some pre-existing knowledge is expected such as;
* Basic Networking
* Linux OS Familiarity (CLI)

## Table of Contents
[TOC]

## Day 0 - Manager: Administrative
This is a manager task to make the appropriate Access Requests.
- [?] SAP Global - SAP Accounts
  * Complete Successfactor Hiring Manager Wizard from Email.
- [?] Hardware Request - Chuck
  * Request Developer Mac
- [X] CORE
  * SMS Reliability New User https://sap.sharepoint.com/sites/207743/SitePages/SMS-Reliablity-Service-Catalog.aspx#sc-core-services-requests
    * Role: `rbac_core_scs_sei_1`
  * Gitlab URL: https://gitlab.core.sapns2.us/scs-sei-internal/
- [X] AWS Development Account Access - Louis
    * URL: https://dev-cs-interns-l.signin.aws.amazon.com/console
- [ ] Add to Calendar Invites (Resend Invites)
- [ ] Sharepoint ?
- [ ] Schedule Welcome Call
- [ ] Schedule Workflow Call
- [ ] Schedule Team History Call
- [ ] Schedule Macbook Setup Call
- [ ] Schedule Manager 1:1 Goals Call
- [ ] Send Welcome Email

## Day 0 - Manager: Hardware Requests
Managers should make [hardware requests to Ariba](https://s1-2-eu.ariba.com/gb/?realm=SAPGLOBAL&locale=en_US)
- [?] Yubikey

## Day 0 - Employee: IT Setup
When you receive your laptop you should receive a ***"New SAP Computer"*** printout with the following text:

```
You will receive an email ______ before your start date with instructions to book your remote IT onboarding session.

During your IT onboarding session you will meet with an IT expert to configure your computer.

Do not turn on your new computer until you meet with the IT expert.
```

If you did not receive this notice or any booking email. Please contact IT at:
> * email: <EMAIL>
> * phone: ******-661-6118 (us)
> * [IT Onboarding](https://one.int.sap/me@sap/itforyou/it_onboarding)

## Day 1 - Mentor: Confirm Access
Confirm the following access requests are complete or in progress:
- [ ] CORE
- [X] CAM [LINK](../sets/reference/cam/CAM-Profile-Requests.md)

## Day 1 - Employee / Mentor: Hardware Requests
Ensure the following hardware has been requested for you. Mentors should help with Ariba Walkthrough:
- [ ] RSA Soft Token (See section below on Hard/Soft Selection)
- [ ] Yubikey

## Day 1 - Employee: Setting up RSA
* Open website: [LINK](https://sap.sharepoint.com/sites/ITforYou/SitePages/RSA-SecurID.aspx?startedResponseCatch=true)
* Order Soft Token
  * [Soft Token Request](https://itsupportportal.services.sap/itsupport?id=kb_article_view&sysparm_article=KB0013426&sys_kb_id=0035ae9dc3e316902f44578f05013139&spa=1)

## Day 1 - Employee: HR Training
Mentors should discover the assigned ***Global*** SAP Training assigned to the user and adjust schedules accordingly.
* 3 days of training reserved
* Additional training courses assigned.
* Link to [Success Factors Learning](https://performancemanager5.successfactors.eu/sf/learning)
* SAP Essentials

## Day 1 - Employee: Review Team Structure and Schedules
At this time, Employees should have scheduled meetings to cover the following topics:
* [Vacation and Leave](https://one.int.sap/me@sap/vacation_and_absences)
  * Sick Days
  * Holidays
- [ ] Welcome Call
- [ ] Workflow Call
- [ ] Team History Call
- [ ] Macbook Setup Call
- [ ] Schedule Manager One on One and Goals


## Day 1 - Employee: Workstation (Macbook)
* <details><summary>Knowledge Validation Checklist</summary><p>

  Validate that you understand Basic Mac usage:
  - [ ] How to install applications
  - [ ] How to use Finder
  - [ ] How to use Spotlight
  - [ ] How to enable "Permissions"
  - [ ] Using the Home Folder or `cd ~`
  - [ ] List Command `ls`
  - [ ] environment and profiles `env`
  - [ ] Shell start scripts `.zshrc` vs `.zshenv`
  </p></details>


## Day 1 - Applications for your Workstation (Macbook)
Install the following applications on your Macbook.

* [Homebrew](../sets/reference/homebrew.md)
* Browser of Choice
    * [Chrome](https://www.google.com/chrome/us/download-chrome)
    * [Firefox](https://www.mozilla.org/en-US/firefox/new/)
* [Iterm2](../sets/reference/iterm2.md)
* [Git](../sets/reference/git.md)
* [Optional - Viscosity OpenVPN Client](../sets/reference/viscosity.md)
* [VSCode or other IDE of Choice](../sets/reference/vscode.md)
* [Docker](../sets/reference/docker.md)
* Password Manager of Choice
  * `Password Depot` is offered through `Self Service`
* Slack App
  * `Slack` is offered through `Self Service`
* [AWS CLI](../sets/reference/awscli.md)
~~* [SAML2AWS](../sets/reference/aws/saml2aws.md)~~
* Optional
  * [Python/Pip(Optional)](../sets/reference/python_pip.md)
  * [Github Desktop(Optional)](../sets/reference/github_desktop.md)

## Day 1 - Employee: Request Slack Access
End-user must request their own slack access.  Follow the instructions below.
* https://wiki.one.int.sap/wiki/display/SAPSCS/Requesting+Access+to+Slack
* Once you receive approval, complete your access by doing the following:
  ```
  Navigate to https://sap.enterprise.slack.com/
  Search for "Sovereign"
  Select "Launch in Slack"
  ```

## Day 1 - Employee: Software Licence Check
Ensure you have actived Software Licenses for the following [LINK](https://sapit-home-prod-004.launchpad.cfapps.eu10.hana.ondemand.com/site?sap-ushell-config=lean#mysoftware-Display?sap-ui-app-id-hint=fe5ca0d0-9694-4ad9-89bb-97471eaa03d7):
- [ ] Docker License
- [ ] Slack
- [ ] RSA Token

## Day 1 - Employee: Configure Gitlab Repositories (Cloning to Workstation)
1. [Gitlab URL](https://gitlab.core.sapns2.us)
1. Click CORE SSO
1. Sign in with your I-Number
1. [Configure Repo Access](../sets/reference/gitlab/Workstation-Setup.md)

## Day 1 - Employee: Setup Docker Workstation
Pull the docker images that will be your executable environment
* Follow instructions on this page: [LINK](../sets/reference/docker/Workstation-Setup.md)

## Day 1 - Employee: AWS Setup
Validate your access to the required environments
* AWS Commercial - https://dev-cs-interns-l.signin.aws.amazon.com/console
Reference Links:
* [Install AWS CLI](../sets/reference/awscli.md)
~~* [nstall SAML2AWS](../sets/reference/aws/saml2aws.md.md)~~
~~* [Validate AWS Access](../sets/reference/aws/saml2aws.md#validate-aws-cli-access)~~

## Day 1 - Employee: Linkedin Learning
Create and Validate your Linkedin Learning Account
* [Linkedin Learning](https://www.linkedin.com/learning)

## Day 1 - Mentor/Intern: Project Presentation
Present and review learning Project
* [Intern Project](./intern-project-2025.md)

## Day X - Training: Git / Gitlab
* [Creating A Merge Request](../sets/gitlab/Creating-A-Merge-Request.md)

## Day X - Training: Computing
* [Introduction to Computing](../sets/training/computing/Introduction%20to%20Computing.md)
* [Introduction to Storage](../sets/training/computing/Introduction%20to%20Storage.md)
* [Introduction to Linux CLI](../sets/training/computing/Introduction%20to%20Linux%20Command%20Line.md)

## Day X - Training: Networking
* [Introduction to Networking](../sets/training/networking/Introduction%20to%20Networking.md)

## Day X - Training: AWS
* [Learn AWS Basics](../sets/training/aws/aws_basics.md)
* [Basics Validation](../sets/training/aws/aws_basics_validation.md)
* Advanced - TBD

## Day X - Training: Certificates
* Certificates
* AWS Private Certificate Authority

## Day X - Training: Terraform
* [Basic Terraform Commands](../sets/terraform/basic%20terraform%20commands.md)
* [Terraform Basics](../sets/terraform/Terraform%20Basics.md)
* [Terraform Layering](../sets/terraform/Terraform%20Layering.md)
* [Terraform Loops](../sets/terraform/terraform%20loops.md)
* [Terraform Ternary and Conditionals](../sets/terraform/terraform%20ternary.md)
* [Terraform Basics Validation](../sets/terraform/terraform_training_project_1.md)

## Day X - Training: Ansible
* [Introduction to YAML](../sets/tooling_and_formats/yaml.md)
* [Ansible Basics pt1](../sets/training/ansible/ansible_basics_1.md)
* [Ansible Basics pt2](../sets/training/ansible/ansible_basics_2.md)
* [Ansible Basics pt3](../sets/training/ansible/ansible_basics_3.md)
* [Ansible Basics pt4](../sets/training/ansible/ansible_basics_4.md)
* [Ansible Basics pt5](../sets/training/ansible/ansible_basics_5.md)
* [Basics Inventories](../sets/training/ansible/ansible_inventories.md)
* Advanced Ansible TBD - Loops, etc.
* [Ansible Basics Validation](../sets/ansible/ansible_training_project_1.md)


## _____UNDER Contruction____
## Day X - Training: OS (Linux)
## Day X - Training: Authentication, Authorization, and Accounting (AAA)
  * Identity and Identity Management
## Day X - Training: Certifiates
## Day X - Training: Docker / Containers
## Day X - Training: Kubernetes (K8s)
  * ArgoCD
  * Helm
## Day X - Training: Prometheus

## Appendix - Bookmarks
* [Bookmarks](https://gitlab.core.sapns2.us/stc-global/onboarding-and-training/general/-/blob/main/FAQs/bookmarks.md?ref_type=heads)
## Bookmarks:
* [CORE Account Management](https://sso.core.sapns2.us/auth/realms/core/account/)
* [AWS GovCloud Access](https://sso.core.sapns2.us/auth/realms/core/protocol/saml/clients/aws-govcloud)
  * ibp-dev-g (************)
* [CORE Gitlab](https://gitlab.core.sapns2.us/)
  * [IBP Dev Repository](https://gitlab.core.sapns2.us/dev/ibp)
  * [PCE Global Repository](https://gitlab.core.sapns2.us/scs/s4pce)
