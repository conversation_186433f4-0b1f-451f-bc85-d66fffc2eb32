awsprof () {   # set aws profile environment variable
  # validate that commandline arguments are passed
  if [ ! $# -eq 1 ]; then
    echo "[Error]: must pass exactly one argument. This argument contains the name of the aws profile to set."
    return 1
  fi

  INPUT_PROFILE=$1

  # check for show profile option
  if [[ $INPUT_PROFILE == "show" ]]; then
    echo "Current AWS_PROFILE:    $AWS_PROFILE"
    return 0
  fi

  # check for list profile option
  if [[ $INPUT_PROFILE == "list" ]]; then
    echo "Available AWS profiles:"
    echo "------------------------------------------"
    for profile in $( cat ~/.aws/config | grep '\[profile ' | cut -c 10- | rev | cut -c 2- | rev )
    do
      echo "$profile"
    done
    return 0
  fi

  # check for default profile
  if [[ $INPUT_PROFILE == "default" ]]; then
    unset AWS_PROFILE
    echo "unset AWS_PROFILE. using 'default'"
    return 0
  fi

  # traverse available AWS profiles
  for profile in $( cat ~/.aws/config | grep '\[profile ' | cut -c 10- | rev | cut -c 2- | rev )
  do
    if [[ $INPUT_PROFILE == $profile ]]; then
      export AWS_PROFILE=$INPUT_PROFILE
      echo "AWS_PROFILE=$INPUT_PROFILE"
      return 0
    fi
  done

  # report error if not found
  echo "[Error]: AWS profile '$INPUT_PROFILE' does not exist within ~/.aws/config"
  echo ""
  echo "Below is a list of available AWS profiles:"
  echo "------------------------------------------"
  for profile in $( cat ~/.aws/config | grep '\[profile ' | cut -c 10- | rev | cut -c 2- | rev )
  do
    echo "$profile"
  done
  echo ""
  echo "One can also execute the following to show the current aws profile:"
  echo "    awsprof show"
  echo ""
  return 1
}
