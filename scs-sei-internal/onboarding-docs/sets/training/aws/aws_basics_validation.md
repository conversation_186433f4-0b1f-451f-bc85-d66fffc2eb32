### Summary

Create an AWS Network to validate basic knowledge.

## Estimated Time
1 Hour

### Repository / Environment
* ___TO BE PROVIDED BY MENTOR/MANAGERS___


### Instructions
1. Use the AWS Console for the requested changes.  We will not use the CLI at this time.
1. [Access AWS Console:](https://sso.core.sapns2.us/auth/realms/core/protocol/saml/clients/aws-govcloud)
    * Log into the Account provided by your `Mentor/Manager`
1. Perform the requested changes below

* **NOTE:** Do **NOT** modify or use an existing environment, environment state, or code.  You may use them as reference, but do not simply copy, clone, or branch existing code as your solution.

### Requested enhancement and/or example solution

- [ ] Create a VPC
- [ ] Deploy 1 public subnet
  - [ ] Create necessary Internet Gateways and Routes 
- [ ] Deploy 1 private subnet
  - [ ] Create necessary NAT Gateways and Routes 
- [ ] Deploy a public instance
- [ ] Deploy a private instance
- [ ] Create a security group
  - [ ] Associate it to the public instance
- [ ] Create another security group
  - [ ] Associate it to the private instance
- [ ] SSH into the Public Instance
- [ ] SSH into the Private Instance from the Public Instance.
- [ ] Create an S3 Bucket.
- [ ] Place a file in the S3 Bucket...
  - [ ] ...from your laptop
  - [ ] ...from the public instance
  - [ ] ...from the private instance.

- [ ] Destroy all your resources
  - [ ] S3 Bucket
  - [ ] Private Instance
  - [ ] Public Instance
  - [ ] Security Group 1
  - [ ] Security Group 2
  - [ ] Private Subnet
  - [ ] Public Subnet
  - [ ] NAT Gateway
  - [ ] Internet Gateway
  - [ ] Additional Route Tables
  - [ ] VPC
### References and Relevant links
* N/A
